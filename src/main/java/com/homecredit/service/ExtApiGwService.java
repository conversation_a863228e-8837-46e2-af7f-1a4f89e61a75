package com.homecredit.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homecredit.config.ApplicationConfig;
import com.homecredit.enumeration.Agent;
import com.homecredit.model.gwservice.CreativeResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExtApiGwService {

    private final ApplicationConfig applicationConfig;
    private final ObjectMapper objectMapper;
    private final DataSource dataSource;

    public Long getTplId(long id, String creativeId, Agent agent) {
        log.debug("Obtaining TPL ID via REST API call. Message ID {}", id);
        String response;
        try {
            HttpURLConnection urlConnection = getHttpURLConnection(creativeId);
            int httpResponseCode = urlConnection.getResponseCode();
            if (httpResponseCode != HttpURLConnection.HTTP_OK) {
                // Use getErrorStream() for non-2xx responses to get the actual error response body
                InputStream errorStream = urlConnection.getErrorStream();
                String errorResponse = errorStream != null ? getResponseAsString(errorStream) : "No error response body";
                log.warn("Failed to obtain TPL ID. Http status code: {}. Response: {}", httpResponseCode, errorResponse);
                return null;
            }
            response = getResponseAsString(urlConnection.getInputStream());
            log.debug("REST API response JSON: {}", response);

        } catch (IOException e) {
            log.warn("Exception occurred during REST API call: {}", e.getMessage(), e);
            return null;
        }

        CreativeResponse creativeResponse;
        try {
            creativeResponse = objectMapper.readValue(response, CreativeResponse.class);
        } catch (JsonProcessingException e) {
            log.warn("Exception occurred while parsing response: {}", e.getMessage());
            return null;
        }

        String creativeCd = creativeResponse.getExternalCode();
        Integer templateId = creativeResponse.getAttributes().stream()
                .filter(it -> it.getIdentityCode().equalsIgnoreCase("template_id"))
                .map(it -> (Integer) it.getValue())
                .findFirst().orElse(null);
        if (templateId == null) {
            log.warn("Could not find TPL ID for Creative ID {}", creativeId);
            return null;
        }
        String channelCd = agent.name();

        log.debug("Creating record in CREATIVE2TEMPLATE table: creativeId={}, creativeCd={}, templateId={}, channelCd={} ",
                creativeId, creativeCd, templateId, channelCd);
        try (Connection conn = dataSource.getConnection()) {
            // query is same for agents using this service, putting hardcoded SMS
            try (PreparedStatement stmt = conn.prepareStatement(applicationConfig.getQuery().get(Agent.SMS).getInsertToCreativeTable())) {
                stmt.setString(1, creativeId);
                stmt.setString(2, creativeCd);
                stmt.setLong(3, templateId.longValue());
                stmt.setString(4, channelCd);
                stmt.executeUpdate();
            }
        } catch (SQLException e) {
            log.error("Exception during executing query", e);
        }
        log.debug("Updating current message with ID {} with new TPL ID: {}", id, templateId);
        return templateId.longValue();
    }

    private HttpURLConnection getHttpURLConnection(String creativeId) throws IOException {
        URL url = new URL(applicationConfig.getExtapigwservice().getUrl() + creativeId);
        HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
        urlConnection.setRequestProperty("Authorization", "Bearer " + applicationConfig.getExtapigwservice().getToken());
        urlConnection.setInstanceFollowRedirects(false);
        urlConnection.setRequestMethod("GET");
        urlConnection.setRequestProperty("Content-Type", "application/vnd.sas.design.publish.reps.v1.creative+json");
        urlConnection.setRequestProperty("charset", "utf-8");
        urlConnection.setUseCaches(false);
        urlConnection.setConnectTimeout(applicationConfig.getExtapigwservice().getConnectTimeout());
        urlConnection.setReadTimeout(applicationConfig.getExtapigwservice().getReadTimeout());
        return urlConnection;
    }

    private String getResponseAsString(InputStream is) throws IOException {
        //read the response
        BufferedReader inBR = new BufferedReader(new InputStreamReader(is));
        String inputLine;
        StringBuilder response = new StringBuilder();

        while ((inputLine = inBR.readLine()) != null) {
            response.append(inputLine);
        }

        inBR.close();

        return response.toString();
    }
}
